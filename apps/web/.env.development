NEXT_PUBLIC_ENV=test
NEXT_PUBLIC_CLIENTID=bd0961693c
NEXT_PUBLIC_SECRET=04ae44cf19c64e1cbed7e1675e71fa76
NEXT_PUBLIC_AUTH_PATH=http://ssodemo.it.test.sankuai.com/sso/web/auth
NEXT_PUBLIC_AUTH_API_URL=http://ssosv.it.test.sankuai.com/open/api/session/userinfo

# Mario WebSocket 配置 - 本地Python LangGraph服务 (AG-UI协议)
MARIO_WS_ENDPOINT=ws://localhost:8080/ws/ag-ui
NEXT_PUBLIC_MARIO_WS_URL=ws://localhost:8080/ws/ag-ui
NEXT_PUBLIC_MARIO_WEBSOCKET_URL=ws://localhost:8080/ws

'use client';

import { useEffect, useRef, useState, useCallback } from 'react';
import type {
  AgentMessage,
  ChatInput,
  WebSocketConnectionState,
  LoadingState,
  UseAgentWebSocketReturn,
  UseAgentWebSocketOptions,
  WebSocketEvent,
  AgentWebSocketError
} from '@/lib/types/chat';
import { CHAT_CONSTANTS } from '@/lib/types/chat';

/**
 * 生成唯一ID
 */
function generateId(): string {
  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
}

/**
 * 通用 Agent WebSocket Hook
 * 支持多种 Agent 的 WebSocket 连接和消息处理
 */
export function useAgentWebSocket(options: UseAgentWebSocketOptions): UseAgentWebSocketReturn {
  const {
    agentConfig,
    onContainerCreated,
    onError,
    onMessage
  } = options;

  // 状态管理
  const [connectionState, setConnectionState] = useState<WebSocketConnectionState>({
    isConnected: false,
    isConnecting: false,
    error: null
  });

  const [loadingState, setLoadingState] = useState<LoadingState>({
    isLoading: false,
    loadingMessage: ''
  });

  const [messages, setMessages] = useState<AgentMessage[]>([]);
  const [currentStreamingMessage, setCurrentStreamingMessage] = useState<string>('');

  // Refs
  const wsRef = useRef<WebSocket | null>(null);
  const currentMessageIdRef = useRef<string | null>(null);
  const currentStreamingMessageRef = useRef<string>('');
  const threadIdRef = useRef<string>(generateId());
  const runIdRef = useRef<string | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectAttemptsRef = useRef<number>(0);

  /**
   * 连接到 Agent WebSocket 服务
   */
  const connect = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      return;
    }

    if (!agentConfig.websocketUrl) {
      const error = `Agent ${agentConfig.id} 没有配置 WebSocket URL`;
      setConnectionState(prev => ({ ...prev, error }));
      onError?.(error);
      return;
    }

    setConnectionState(prev => ({ ...prev, isConnecting: true, error: null }));

    try {
      const ws = new WebSocket(agentConfig.websocketUrl);

      ws.onopen = () => {
        console.log(`Agent ${agentConfig.id} WebSocket connected`);
        setConnectionState({
          isConnected: true,
          isConnecting: false,
          error: null,
          lastConnectedAt: new Date()
        });
        reconnectAttemptsRef.current = 0;
      };

      ws.onclose = (event) => {
        console.log(`Agent ${agentConfig.id} WebSocket disconnected:`, event.reason);
        setConnectionState(prev => ({
          ...prev,
          isConnected: false,
          isConnecting: false
        }));
        
        // 自动重连逻辑
        if (reconnectAttemptsRef.current < CHAT_CONSTANTS.MAX_RECONNECT_ATTEMPTS) {
          reconnectAttemptsRef.current++;
          reconnectTimeoutRef.current = setTimeout(() => {
            console.log(`Attempting to reconnect to ${agentConfig.id} (attempt ${reconnectAttemptsRef.current})`);
            connect();
          }, CHAT_CONSTANTS.DEFAULT_WEBSOCKET_RECONNECT_DELAY);
        }
      };

      ws.onerror = (error) => {
        console.error(`Agent ${agentConfig.id} WebSocket error:`, error);
        const errorMessage = `Agent ${agentConfig.name} 连接失败`;
        setConnectionState({
          isConnected: false,
          isConnecting: false,
          error: errorMessage
        });
        onError?.(errorMessage);
      };

      ws.onmessage = (event) => {
        try {
          const wsEvent: WebSocketEvent = JSON.parse(event.data);
          wsEvent.agentId = agentConfig.id; // 添加 agentId 标识
          handleWebSocketEvent(wsEvent);
        } catch (error) {
          console.error(`Failed to parse ${agentConfig.id} WebSocket event:`, error);
        }
      };

      wsRef.current = ws;
    } catch (error) {
      console.error(`Failed to create ${agentConfig.id} WebSocket connection:`, error);
      setConnectionState({
        isConnected: false,
        isConnecting: false,
        error: `无法创建到 ${agentConfig.name} 的连接`
      });
    }
  }, [agentConfig, onError]);

  /**
   * 处理 WebSocket 事件
   */
  const handleWebSocketEvent = useCallback((event: WebSocketEvent) => {
    console.log(`Received ${agentConfig.id} event:`, event);

    switch (event.type) {
      case 'run_started':
        runIdRef.current = event.run_id || null;
        setLoadingState({ isLoading: true, loadingMessage: '正在处理...' });
        break;

      case 'run_finished':
        setLoadingState({ isLoading: false, loadingMessage: '' });
        setCurrentStreamingMessage('');
        currentStreamingMessageRef.current = '';
        currentMessageIdRef.current = null;
        break;

      case 'text_message_start':
        currentMessageIdRef.current = event.message_id || generateId();
        currentStreamingMessageRef.current = '';
        setCurrentStreamingMessage('');
        break;

      case 'text_message_delta':
        if (event.message_id === currentMessageIdRef.current && event.delta) {
          currentStreamingMessageRef.current += event.delta;
          setCurrentStreamingMessage(currentStreamingMessageRef.current);
        }
        break;

      case 'text_message_end':
        if (event.message_id === currentMessageIdRef.current) {
          const finalContent = currentStreamingMessageRef.current;
          const newMessage: AgentMessage = {
            id: event.message_id || generateId(),
            role: 'assistant',
            content: finalContent,
            timestamp: new Date(),
            type: 'text',
            agentId: agentConfig.id,
            agentType: agentConfig.type,
            model: agentConfig.defaultModel
          };

          setMessages(prev => [...prev, newMessage]);
          onMessage?.(newMessage);
          setCurrentStreamingMessage('');
          currentStreamingMessageRef.current = '';
          currentMessageIdRef.current = null;
        }
        break;

      case 'tool_calls_start':
        if (event.tool_calls && event.tool_calls.length > 0) {
          const toolMessage: AgentMessage = {
            id: generateId(),
            role: 'assistant',
            content: `正在调用工具: ${event.tool_calls.map(tc => tc.function?.name || tc.name).join(', ')}`,
            timestamp: new Date(),
            type: 'tool_call',
            agentId: agentConfig.id,
            agentType: agentConfig.type,
            metadata: { tool_calls: event.tool_calls }
          };
          setMessages(prev => [...prev, toolMessage]);
          onMessage?.(toolMessage);
        }
        break;

      case 'tool_calls_end':
        if (event.tool_call_results && event.tool_call_results.length > 0) {
          const resultMessage: AgentMessage = {
            id: generateId(),
            role: 'assistant',
            content: '工具调用完成',
            timestamp: new Date(),
            type: 'tool_result',
            agentId: agentConfig.id,
            agentType: agentConfig.type,
            metadata: { tool_results: event.tool_call_results }
          };
          setMessages(prev => [...prev, resultMessage]);
          onMessage?.(resultMessage);
        }
        break;

      case 'container_created':
        // 处理容器创建事件（特定于某些 Agent）
        if (event.snapshot?.containerId && event.snapshot?.port) {
          onContainerCreated?.(event.snapshot.containerId, event.snapshot.port);
        }
        break;

      case 'error':
        const errorMessage: AgentMessage = {
          id: generateId(),
          role: 'assistant',
          content: `错误: ${event.error}`,
          timestamp: new Date(),
          type: 'error',
          agentId: agentConfig.id,
          agentType: agentConfig.type
        };
        setMessages(prev => [...prev, errorMessage]);
        onMessage?.(errorMessage);
        setLoadingState({ isLoading: false, loadingMessage: '' });
        onError?.(event.error || '未知错误');
        break;

      case 'interrupt':
        const interruptMessage: AgentMessage = {
          id: generateId(),
          role: 'assistant',
          content: `中断: ${event.error || '操作被中断'}`,
          timestamp: new Date(),
          type: 'interrupt',
          agentId: agentConfig.id,
          agentType: agentConfig.type
        };
        setMessages(prev => [...prev, interruptMessage]);
        onMessage?.(interruptMessage);
        break;

      default:
        console.log(`Unhandled ${agentConfig.id} event type:`, event.type);
    }
  }, [agentConfig, onError, onContainerCreated, onMessage]);

  /**
   * 断开连接
   */
  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
    
    if (wsRef.current) {
      wsRef.current.close();
      wsRef.current = null;
    }
    
    setConnectionState({
      isConnected: false,
      isConnecting: false,
      error: null
    });
    
    reconnectAttemptsRef.current = 0;
  }, []);

  /**
   * 发送消息到 Agent WebSocket
   */
  const sendMessage = useCallback(
    async (input: ChatInput) => {
      if (!input.message.trim()) {
        onError?.('消息内容不能为空');
        return;
      }

      if (!wsRef.current || wsRef.current.readyState !== WebSocket.OPEN) {
        onError?.(`与 ${agentConfig.name} 的连接未建立`);
        return;
      }

      // 添加用户消息到列表
      const userMessage: AgentMessage = {
        id: generateId(),
        role: 'user',
        content: input.message,
        timestamp: new Date(),
        type: 'text',
        agentId: agentConfig.id,
        agentType: agentConfig.type,
        model: input.model || agentConfig.defaultModel,
        metadata: input.metadata
      };

      setMessages(prev => [...prev, userMessage]);
      onMessage?.(userMessage);
      setLoadingState({ isLoading: true, loadingMessage: '正在处理...' });

      // 生成新的运行ID
      runIdRef.current = generateId();

      try {
        // 构建 WebSocket 请求（支持不同 Agent 的协议格式）
        const wsRequest = {
          type: 'run',
          messages: [
            {
              role: 'user',
              content: input.message.trim()
            }
          ],
          thread_id: threadIdRef.current,
          run_id: runIdRef.current,
          tools: [],
          state: {
            agentId: agentConfig.id,
            agentType: agentConfig.type,
            model: input.model || agentConfig.defaultModel,
            ...input.metadata
          }
        };

        wsRef.current.send(JSON.stringify(wsRequest));
        console.log(`Message sent to ${agentConfig.id}:`, wsRequest);
      } catch (error) {
        console.error(`Failed to send message to ${agentConfig.id}:`, error);
        onError?.(`发送消息到 ${agentConfig.name} 失败`);
        setLoadingState({ isLoading: false, loadingMessage: '' });
      }
    },
    [agentConfig, onError, onMessage]
  );

  /**
   * 停止当前操作
   */
  const stopCurrentOperation = useCallback(() => {
    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify({
        type: 'interrupt_response',
        response: 'stop'
      }));
      setLoadingState({ isLoading: false, loadingMessage: '' });
      setCurrentStreamingMessage('');
      currentStreamingMessageRef.current = '';
      currentMessageIdRef.current = null;
    }
  }, []);

  /**
   * 清空消息
   */
  const clearMessages = useCallback(() => {
    setMessages([]);
    setCurrentStreamingMessage('');
    currentStreamingMessageRef.current = '';
    currentMessageIdRef.current = null;
    // 重置线程ID
    threadIdRef.current = generateId();
  }, []);

  /**
   * 重新连接
   */
  const reconnect = useCallback(() => {
    disconnect();
    setTimeout(connect, 1000);
  }, [disconnect, connect]);

  // 清理函数
  useEffect(() => {
    return () => {
      disconnect();
    };
  }, [disconnect]);

  return {
    // 连接状态
    isConnected: connectionState.isConnected,
    isConnecting: connectionState.isConnecting,
    connectionError: connectionState.error,

    // 加载状态
    isLoading: loadingState.isLoading,
    loadingMessage: loadingState.loadingMessage,

    // 消息
    messages,
    currentStreamingMessage: '',

    // 方法
    sendMessage,
    stopCurrentOperation: () => {
      // 停止当前操作的实现
      setLoadingState({ isLoading: false, loadingMessage: '' });
    },
    clearMessages,
    connect,
    disconnect,
    reconnect: connect
  };
}

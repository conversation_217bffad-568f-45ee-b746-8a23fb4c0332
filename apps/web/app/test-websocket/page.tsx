'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@workspace/ui/components/button';
import { Input } from '@workspace/ui/components/input';
import { Card, CardContent, CardHeader, CardTitle } from '@workspace/ui/components/card';
import { Badge } from '@workspace/ui/components/badge';

/**
 * WebSocket 测试页面
 * 用于测试 AG-UI WebSocket 连接
 */
export default function TestWebSocketPage() {
  const [wsUrl, setWsUrl] = useState('ws://localhost:8080/ws/ag-ui');
  const [ws, setWs] = useState<WebSocket | null>(null);
  const [connectionState, setConnectionState] = useState<'disconnected' | 'connecting' | 'connected' | 'error'>('disconnected');
  const [messages, setMessages] = useState<string[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [error, setError] = useState<string | null>(null);

  // 连接 WebSocket
  const connect = () => {
    if (ws?.readyState === WebSocket.OPEN) {
      return;
    }

    setConnectionState('connecting');
    setError(null);
    setMessages(prev => [...prev, `🔌 正在连接到: ${wsUrl}`]);

    try {
      const websocket = new WebSocket(wsUrl);

      websocket.onopen = () => {
        console.log('WebSocket connected');
        setConnectionState('connected');
        setMessages(prev => [...prev, '✅ WebSocket 连接成功']);
      };

      websocket.onclose = (event) => {
        console.log('WebSocket disconnected:', event);
        setConnectionState('disconnected');
        setMessages(prev => [...prev, `❌ WebSocket 连接关闭 (code: ${event.code}, reason: ${event.reason})`]);
      };

      websocket.onerror = (error) => {
        console.error('WebSocket error:', error);
        setConnectionState('error');
        setError('WebSocket 连接错误');
        setMessages(prev => [...prev, '❌ WebSocket 连接错误']);
      };

      websocket.onmessage = (event) => {
        console.log('WebSocket message:', event.data);
        try {
          const data = JSON.parse(event.data);
          setMessages(prev => [...prev, `📨 收到消息: ${JSON.stringify(data, null, 2)}`]);
        } catch (e) {
          setMessages(prev => [...prev, `📨 收到消息: ${event.data}`]);
        }
      };

      setWs(websocket);
    } catch (error) {
      console.error('Failed to create WebSocket:', error);
      setConnectionState('error');
      setError('创建 WebSocket 连接失败');
      setMessages(prev => [...prev, '❌ 创建 WebSocket 连接失败']);
    }
  };

  // 断开连接
  const disconnect = () => {
    if (ws) {
      ws.close();
      setWs(null);
    }
    setConnectionState('disconnected');
    setMessages(prev => [...prev, '🔌 手动断开连接']);
  };

  // 发送消息
  const sendMessage = () => {
    if (!ws || ws.readyState !== WebSocket.OPEN) {
      setMessages(prev => [...prev, '❌ WebSocket 未连接，无法发送消息']);
      return;
    }

    if (!inputMessage.trim()) {
      return;
    }

    try {
      const message = {
        type: 'run',
        messages: [
          {
            role: 'user',
            content: inputMessage.trim()
          }
        ],
        thread_id: `test-thread-${Date.now()}`,
        tools: [],
        state: {}
      };

      ws.send(JSON.stringify(message));
      setMessages(prev => [...prev, `📤 发送消息: ${inputMessage}`]);
      setInputMessage('');
    } catch (error) {
      console.error('Failed to send message:', error);
      setMessages(prev => [...prev, '❌ 发送消息失败']);
    }
  };

  // 清空消息
  const clearMessages = () => {
    setMessages([]);
  };

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      if (ws) {
        ws.close();
      }
    };
  }, [ws]);

  const getConnectionBadge = () => {
    switch (connectionState) {
      case 'connected':
        return <Badge className="bg-green-500">已连接</Badge>;
      case 'connecting':
        return <Badge className="bg-yellow-500">连接中</Badge>;
      case 'error':
        return <Badge className="bg-red-500">错误</Badge>;
      default:
        return <Badge variant="secondary">未连接</Badge>;
    }
  };

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <h1 className="text-3xl font-bold mb-6">WebSocket 连接测试</h1>
      
      {/* 连接控制 */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            WebSocket 连接控制
            {getConnectionBadge()}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Input
              value={wsUrl}
              onChange={(e) => setWsUrl(e.target.value)}
              placeholder="WebSocket URL"
              className="flex-1"
            />
            <Button 
              onClick={connect} 
              disabled={connectionState === 'connecting' || connectionState === 'connected'}
            >
              连接
            </Button>
            <Button 
              onClick={disconnect} 
              variant="outline"
              disabled={connectionState === 'disconnected'}
            >
              断开
            </Button>
          </div>
          
          {error && (
            <div className="text-red-500 text-sm">
              错误: {error}
            </div>
          )}
        </CardContent>
      </Card>

      {/* 消息发送 */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>发送测试消息</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-2">
            <Input
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              placeholder="输入测试消息..."
              className="flex-1"
              onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
            />
            <Button 
              onClick={sendMessage}
              disabled={connectionState !== 'connected' || !inputMessage.trim()}
            >
              发送
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 消息日志 */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>消息日志</CardTitle>
          <Button onClick={clearMessages} variant="outline" size="sm">
            清空
          </Button>
        </CardHeader>
        <CardContent>
          <div className="bg-gray-50 p-4 rounded-lg max-h-96 overflow-y-auto">
            {messages.length === 0 ? (
              <div className="text-gray-500 text-center">暂无消息</div>
            ) : (
              <div className="space-y-2">
                {messages.map((message, index) => (
                  <div key={index} className="text-sm font-mono">
                    <span className="text-gray-500">[{new Date().toLocaleTimeString()}]</span> {message}
                  </div>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
